#pragma once

#include "types.h"
#include "config.h"
#include "auth.h"
#include "api.h"
#include "downloader.h"
#include "storage.h"
#include "cli.h"
#include <memory>
#include <functional>

namespace pixiv {

/**
 * @brief Application execution result
 */
struct AppResult {
    bool success;
    std::string error_message;
    std::map<std::string, int> statistics;
};

/**
 * @brief Main application class
 * 
 * This class orchestrates the entire download process by coordinating
 * all modules: authentication, API calls, downloading, and storage.
 */
class PixivTagDownloader {
public:
    /**
     * @brief Constructor
     */
    PixivTagDownloader();

    /**
     * @brief Destructor
     */
    ~PixivTagDownloader();

    /**
     * @brief Initialize application with configuration
     * @param config_path Path to configuration file
     * @param cmd_args Command line arguments override
     * @return true if initialization successful
     */
    bool initialize(const std::string& config_path, 
                   const std::map<std::string, std::string>& cmd_args = {});

    /**
     * @brief Run application in interactive mode
     * @return Application result
     */
    AppResult run_interactive();

    /**
     * @brief Run application in command line mode
     * @return Application result
     */
    AppResult run_command_line();

    /**
     * @brief Cancel ongoing operations
     */
    void cancel();

    /**
     * @brief Check if application is busy
     * @return true if operations are ongoing
     */
    bool is_busy() const;

private:
    /**
     * @brief Setup logging system
     * @return true if setup successful
     */
    bool setup_logging();

    /**
     * @brief Initialize authentication
     * @return true if authentication successful
     */
    bool initialize_authentication();

    /**
     * @brief Initialize API client
     * @return true if initialization successful
     */
    bool initialize_api_client();

    /**
     * @brief Initialize downloader
     * @return true if initialization successful
     */
    bool initialize_downloader();

    /**
     * @brief Initialize storage manager
     * @return true if initialization successful
     */
    bool initialize_storage_manager();

    /**
     * @brief Get user information and validate UID
     * @param uid User ID to validate
     * @return User information, nullptr if failed
     */
    std::unique_ptr<UserInfo> get_and_validate_user(const std::string& uid);

    /**
     * @brief Get artworks for user
     * @param uid User ID
     * @param progress_callback Progress callback function
     * @return Vector of artworks
     */
    std::vector<ArtworkInfo> get_user_artworks(const std::string& uid,
                                              std::function<void(int, int)> progress_callback = nullptr);

    /**
     * @brief Filter artworks based on configuration
     * @param artworks Vector of artworks to filter
     * @return Filtered vector of artworks
     */
    std::vector<ArtworkInfo> filter_artworks(const std::vector<ArtworkInfo>& artworks);

    /**
     * @brief Execute download process
     * @param artworks Vector of artworks to download
     * @param progress_callback Progress callback function
     * @return true if download successful
     */
    bool execute_download(const std::vector<ArtworkInfo>& artworks,
                         std::function<void(const DownloadProgress&)> progress_callback = nullptr);

    /**
     * @brief Handle interactive tag selection
     * @param available_tags Vector of available tags
     * @return Selected tags and logic
     */
    std::pair<std::vector<std::string>, TagLogic> handle_interactive_tag_selection(
        const std::vector<std::string>& available_tags);

    /**
     * @brief Handle interactive artwork type selection
     * @return Selected artwork types
     */
    std::vector<ArtworkType> handle_interactive_type_selection();

    /**
     * @brief Validate download configuration
     * @return true if configuration is valid
     */
    bool validate_download_configuration();

    /**
     * @brief Generate download summary
     * @param artworks Vector of artworks to download
     * @return Summary statistics
     */
    std::map<std::string, int> generate_download_summary(const std::vector<ArtworkInfo>& artworks);

    // Core components
    std::unique_ptr<AuthManager> auth_manager_;
    std::unique_ptr<PixivApi> api_client_;
    std::unique_ptr<DownloadManager> download_manager_;
    std::unique_ptr<StorageManager> storage_manager_;
    std::unique_ptr<InteractiveUI> interactive_ui_;

    // State
    bool initialized_;
    bool cancelled_;
    std::string last_error_;
    std::map<std::string, int> statistics_;
};

/**
 * @brief Application runner
 * 
 * This class provides the main entry point and handles application lifecycle.
 */
class ApplicationRunner {
public:
    /**
     * @brief Run application with command line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @return Exit code (0 for success, non-zero for error)
     */
    static int run(int argc, char* argv[]);

private:
    /**
     * @brief Parse command line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @return Parsed arguments map
     */
    static std::map<std::string, std::string> parse_arguments(int argc, char* argv[]);

    /**
     * @brief Determine execution mode from arguments
     * @param args Parsed arguments
     * @return true if interactive mode, false for command line mode
     */
    static bool is_interactive_mode(const std::map<std::string, std::string>& args);

    /**
     * @brief Setup signal handlers for graceful shutdown
     * @param app Application instance
     */
    static void setup_signal_handlers(PixivTagDownloader* app);

    /**
     * @brief Display application banner
     */
    static void display_banner();

    /**
     * @brief Display final statistics
     * @param result Application result
     */
    static void display_final_statistics(const AppResult& result);
};

/**
 * @brief Configuration validator
 * 
 * Utility class to validate configuration before running the application.
 */
class ConfigurationValidator {
public:
    /**
     * @brief Validate complete configuration
     * @param config Configuration to validate
     * @return true if configuration is valid
     */
    static bool validate(const Config& config);

    /**
     * @brief Get validation errors
     * @param config Configuration to validate
     * @return Vector of error messages
     */
    static std::vector<std::string> get_validation_errors(const Config& config);

    /**
     * @brief Validate authentication configuration
     * @param config Configuration to validate
     * @return true if authentication config is valid
     */
    static bool validate_authentication(const Config& config);

    /**
     * @brief Validate download configuration
     * @param config Configuration to validate
     * @return true if download config is valid
     */
    static bool validate_download_config(const Config& config);

    /**
     * @brief Validate path templates
     * @param config Configuration to validate
     * @return true if path templates are valid
     */
    static bool validate_path_templates(const Config& config);

private:
    /**
     * @brief Check if required fields are present
     * @param config Configuration to check
     * @return Vector of missing field names
     */
    static std::vector<std::string> check_required_fields(const Config& config);

    /**
     * @brief Validate numeric ranges
     * @param config Configuration to validate
     * @return Vector of range validation errors
     */
    static std::vector<std::string> validate_numeric_ranges(const Config& config);
};

} // namespace pixiv
