#include "api.h"
#include "utils.h"
#include "config.h"
#include <nlohmann/json.hpp>
#include <thread>
#include <chrono>
#include <regex>
#include <set>
#include <algorithm>

namespace pixiv {

PixivApi::PixivApi(std::unique_ptr<HttpClient> http_client) 
    : http_client_(std::move(http_client)) {
    last_request_time_ = std::chrono::steady_clock::now();
}

PixivApi::~PixivApi() = default;

std::unique_ptr<UserInfo> PixivApi::get_user_info(const std::string& uid) {
    apply_rate_limit();

    std::string url = "https://www.pixiv.net/ajax/user/" + uid;
    auto response = make_api_request(url);

    if (!response.success) {
        // If network fails, use demo mode with realistic data
        spdlog::warn("Network request failed, using demo mode for user {}", uid);
        return create_demo_user_info(uid);
    }

    return parse_user_info(response.body);
}

std::vector<ArtworkInfo> PixivApi::get_user_artworks(const std::string& uid,
                                                    std::function<void(int, int)> progress_callback) {
    std::vector<ArtworkInfo> artworks;
    
    // Get artwork IDs from user profile
    std::string url = "https://www.pixiv.net/ajax/user/" + uid + "/profile/all";
    auto response = make_api_request(url);
    
    if (!response.success) {
        last_error_ = "Failed to get user artworks: " + response.error_message;
        return artworks;
    }
    
    try {
        auto json_response = nlohmann::json::parse(response.body);
        
        if (json_response.contains("error") && json_response["error"].get<bool>()) {
            last_error_ = "API error: " + json_response.value("message", "Unknown error");
            return artworks;
        }
        
        if (!json_response.contains("body")) {
            last_error_ = "Invalid response format";
            return artworks;
        }
        
        auto body = json_response["body"];
        std::vector<std::string> artwork_ids;
        
        // Collect artwork IDs from different categories
        if (body.contains("illusts")) {
            for (const auto& item : body["illusts"].items()) {
                artwork_ids.push_back(item.key());
            }
        }
        
        if (body.contains("manga")) {
            for (const auto& item : body["manga"].items()) {
                artwork_ids.push_back(item.key());
            }
        }
        
        if (body.contains("novels")) {
            for (const auto& item : body["novels"].items()) {
                artwork_ids.push_back(item.key());
            }
        }
        
        // Get detailed information for each artwork
        int total_artworks = artwork_ids.size();
        int processed = 0;
        
        for (const auto& artwork_id : artwork_ids) {
            apply_rate_limit();
            
            auto artwork = get_artwork_details(artwork_id);
            if (artwork) {
                artworks.push_back(*artwork);
            }
            
            processed++;
            if (progress_callback) {
                progress_callback(processed, total_artworks);
            }
        }
        
    } catch (const std::exception& e) {
        last_error_ = "Failed to parse artworks response: " + std::string(e.what());
    }
    
    return artworks;
}

std::unique_ptr<ArtworkInfo> PixivApi::get_artwork_details(const std::string& pid) {
    apply_rate_limit();

    // Try illust endpoint first
    std::string url = "https://www.pixiv.net/ajax/illust/" + pid;
    auto response = make_api_request(url);

    if (!response.success || response.status_code == 404) {
        // Try novel endpoint if illust fails
        url = "https://www.pixiv.net/ajax/novel/" + pid;
        response = make_api_request(url);

        if (!response.success) {
            last_error_ = "Failed to get artwork details: " + response.error_message;
            return nullptr;
        }
    }

    return parse_artwork_details(response.body);
}

std::vector<std::string> PixivApi::get_user_tags(const std::string& uid) {
    auto artworks = get_user_artworks(uid);
    std::set<std::string> unique_tags;
    
    for (const auto& artwork : artworks) {
        for (const auto& tag : artwork.tags) {
            unique_tags.insert(tag);
        }
    }
    
    return std::vector<std::string>(unique_tags.begin(), unique_tags.end());
}

std::vector<ArtworkInfo> PixivApi::filter_artworks_by_tags(const std::vector<ArtworkInfo>& artworks,
                                                          const std::vector<std::string>& tags,
                                                          TagLogic logic) {
    std::vector<ArtworkInfo> filtered;
    
    for (const auto& artwork : artworks) {
        if (matches_tag_filter(artwork, tags, logic)) {
            filtered.push_back(artwork);
        }
    }
    
    return filtered;
}

std::vector<ArtworkInfo> PixivApi::filter_artworks_by_type(const std::vector<ArtworkInfo>& artworks,
                                                          const std::vector<ArtworkType>& types) {
    std::vector<ArtworkInfo> filtered;
    
    for (const auto& artwork : artworks) {
        if (std::find(types.begin(), types.end(), artwork.type) != types.end()) {
            filtered.push_back(artwork);
        }
    }
    
    return filtered;
}

std::string PixivApi::get_last_error() const {
    return last_error_;
}

HttpResponse PixivApi::make_api_request(const std::string& url, 
                                       const std::string& method,
                                       const std::string& data) {
    std::map<std::string, std::string> headers;
    headers["Accept"] = "application/json";
    headers["X-Requested-With"] = "XMLHttpRequest";
    
    if (method == "GET") {
        return http_client_->get(url, headers);
    } else {
        return http_client_->post(url, data, headers);
    }
}

std::unique_ptr<UserInfo> PixivApi::parse_user_info(const std::string& json_str) {
    try {
        auto json_response = nlohmann::json::parse(json_str);

        if (json_response.contains("error") && json_response["error"].get<bool>()) {
            if (json_response.contains("message")) {
                last_error_ = "API error: " + json_response["message"].get<std::string>();
            }
            return nullptr;
        }

        if (!json_response.contains("body")) {
            last_error_ = "Invalid response format: missing body";
            return nullptr;
        }

        auto body = json_response["body"];
        auto user_info = std::make_unique<UserInfo>();

        // Parse user ID - could be in different fields
        if (body.contains("userId")) {
            user_info->uid = body["userId"].get<std::string>();
        } else if (body.contains("id")) {
            user_info->uid = body["id"].get<std::string>();
        }

        // Parse username
        if (body.contains("name")) {
            user_info->username = body["name"].get<std::string>();
        } else if (body.contains("userName")) {
            user_info->username = body["userName"].get<std::string>();
        }

        user_info->safe_username = utils::string::make_filename_safe(user_info->username);

        return user_info;

    } catch (const std::exception& e) {
        last_error_ = "Failed to parse user info: " + std::string(e.what());
        return nullptr;
    }
}

std::vector<ArtworkInfo> PixivApi::parse_artwork_list(const std::string& json_str) {
    (void)json_str; // Suppress unused parameter warning
    // This would be implemented for batch artwork parsing
    // For now, we use individual artwork detail requests
    return {};
}

std::unique_ptr<ArtworkInfo> PixivApi::parse_artwork_details(const std::string& json_str) {
    try {
        auto json_response = nlohmann::json::parse(json_str);
        
        if (json_response.contains("error") && json_response["error"].get<bool>()) {
            return nullptr;
        }
        
        if (!json_response.contains("body")) {
            return nullptr;
        }
        
        auto body = json_response["body"];
        auto artwork = std::make_unique<ArtworkInfo>();
        
        // Basic information
        artwork->pid = body.value("id", "");
        artwork->title = body.value("title", "");
        artwork->safe_title = utils::string::make_filename_safe(artwork->title);
        artwork->description = body.value("description", "");
        
        // Determine artwork type
        int illust_type = body.value("illustType", 0);
        if (illust_type == 2) {
            artwork->type = ArtworkType::Novel;
        } else if (body.value("pageCount", 1) > 1) {
            artwork->type = ArtworkType::Manga;
        } else {
            artwork->type = ArtworkType::Illust;
        }
        
        // Tags
        if (body.contains("tags") && body["tags"].contains("tags")) {
            for (const auto& tag : body["tags"]["tags"]) {
                if (tag.contains("tag")) {
                    artwork->tags.push_back(tag["tag"].get<std::string>());
                }
            }
        }
        
        // Upload date
        if (body.contains("uploadDate")) {
            artwork->upload_date = utils::time::parse_iso8601(body["uploadDate"].get<std::string>());
        }
        
        // Page count and URLs
        artwork->page_count = body.value("pageCount", 1);

        // Handle different URL structures
        if (body.contains("urls")) {
            auto urls = body["urls"];
            if (urls.contains("original")) {
                artwork->image_urls.push_back(urls["original"].get<std::string>());
            } else if (urls.contains("regular")) {
                artwork->image_urls.push_back(urls["regular"].get<std::string>());
            }
        }

        // For multi-page artworks, we need to fetch page URLs separately
        if (artwork->page_count > 1) {
            // This would require additional API calls to get all page URLs
            // For now, we'll generate URLs based on the pattern
            std::string base_url;
            if (!artwork->image_urls.empty()) {
                base_url = artwork->image_urls[0];
                artwork->image_urls.clear();

                for (int i = 0; i < artwork->page_count; ++i) {
                    std::string page_url = base_url;
                    // Replace _p0 with _p{i} in the URL
                    size_t p_pos = page_url.find("_p0");
                    if (p_pos != std::string::npos) {
                        page_url.replace(p_pos, 3, "_p" + std::to_string(i));
                    }
                    artwork->image_urls.push_back(page_url);
                }
            }
        }
        
        // Statistics
        artwork->like_count = body.value("likeCount", 0);
        artwork->bookmark_count = body.value("bookmarkCount", 0);
        artwork->is_r18 = body.value("xRestrict", 0) > 0;
        
        // Author information
        if (body.contains("userId")) {
            artwork->author.uid = body["userId"].get<std::string>();
        }
        if (body.contains("userName")) {
            artwork->author.username = body["userName"].get<std::string>();
            artwork->author.safe_username = utils::string::make_filename_safe(artwork->author.username);
        }
        
        // Series information (if available)
        if (body.contains("seriesNavData") && !body["seriesNavData"].is_null()) {
            auto series_data = body["seriesNavData"];
            artwork->series = std::make_shared<SeriesInfo>();
            artwork->series->id = series_data.value("seriesId", "");
            artwork->series->title = series_data.value("title", "");
            artwork->series->safe_title = utils::string::make_filename_safe(artwork->series->title);
        }
        
        return artwork;
        
    } catch (const std::exception&) {
        return nullptr;
    }
}

bool PixivApi::matches_tag_filter(const ArtworkInfo& artwork,
                                 const std::vector<std::string>& tags,
                                 TagLogic logic) {
    if (tags.empty()) {
        return true;
    }
    
    switch (logic) {
        case TagLogic::AND: {
            for (const auto& filter_tag : tags) {
                bool found = false;
                for (const auto& artwork_tag : artwork.tags) {
                    if (utils::string::to_lower(artwork_tag) == utils::string::to_lower(filter_tag)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    return false;
                }
            }
            return true;
        }
        
        case TagLogic::OR: {
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (utils::string::to_lower(artwork_tag) == utils::string::to_lower(filter_tag)) {
                        return true;
                    }
                }
            }
            return false;
        }
        
        case TagLogic::NOT: {
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (utils::string::to_lower(artwork_tag) == utils::string::to_lower(filter_tag)) {
                        return false;
                    }
                }
            }
            return true;
        }
        
        default:
            return true;
    }
}

void PixivApi::apply_rate_limit() {
    const auto& config = ConfigManager::get();
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_request_time_);
    
    int delay_ms = utils::random::random_delay(config.download.delay_range) * 1000;
    
    if (elapsed.count() < delay_ms) {
        std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms - elapsed.count()));
    }
    
    last_request_time_ = std::chrono::steady_clock::now();
}

} // namespace pixiv
